﻿using FlairLoop.Domain.Auth;
using Microsoft.AspNetCore.Authentication.BearerToken;

namespace FlairLoop.Api.Identity;

public static class AccessTokenExtension
{
    public static AccessTokenResponse ToAccessTokenResponse(this AccessTokenInfo accessTokenInfo)
    {
        return new AccessTokenResponse
        {
            AccessToken = accessTokenInfo.AccessToken,
            RefreshToken = accessTokenInfo.RefreshToken,
            ExpiresIn = accessTokenInfo.ExpiresIn,
        };
    }

}
