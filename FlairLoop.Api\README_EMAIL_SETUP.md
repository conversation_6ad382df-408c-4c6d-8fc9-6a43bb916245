# Email Service Setup Guide

This guide explains how to set up the email service using Brevo (formerly SendinBlue) as the SMTP provider.

## Brevo SMTP Setup

1. **Create a Brevo Account**:
   - Go to [Brevo's website](https://www.brevo.com/) and sign up for a free account
   - The free plan includes 300 emails per day

2. **Get SMTP Credentials**:
   - After signing up and logging in, navigate to the SMTP & API section
   - Go to SMTP section and get your SMTP credentials:
     - SMTP Server: smtp-relay.brevo.com
     - Port: 587
     - Login: Your Brevo account email (use <EMAIL>)
     - SMTP Key: Generate an SMTP key from the dashboard

3. **Update Configuration**:
   - In `appsettings.Development.json`, update the Email section with your Brevo SMTP key
   - For production, set the environment variables in your hosting environment

## Configuration Settings

The email service requires the following configuration settings:

```json
"Email": {
  "SmtpServer": "smtp-relay.brevo.com",
  "SmtpPort": 587,
  "SmtpUsername": "<EMAIL>",
  "SmtpPassword": "your-brevo-smtp-key-here",
  "FromEmail": "<EMAIL>",
  "FromName": "FlairLoop",
  "EnableSsl": true
}
```

## Environment Variables for Docker

When running in Docker, set these environment variables:

```
EMAIL_SMTP_SERVER=smtp-relay.brevo.com
EMAIL_SMTP_PORT=587
EMAIL_SMTP_USERNAME=<EMAIL>
EMAIL_SMTP_PASSWORD=your-brevo-smtp-key-here
EMAIL_FROM_EMAIL=<EMAIL>
EMAIL_FROM_NAME=FlairLoop
EMAIL_ENABLE_SSL=true
```

## Testing the Email Service

You can test the email service using the following endpoint:

```http
POST /api/email/send
Content-Type: application/json

{
  "to": "<EMAIL>",
  "subject": "Test Email from FlairLoop",
  "body": "<h1>Hello from FlairLoop!</h1><p>This is a test email sent from the FlairLoop API.</p>",
  "isHtml": true
}
```

## Troubleshooting

If you encounter issues with sending emails:

1. Check that your SMTP credentials are correct
2. Verify that your Brevo account is active and not suspended
3. Check the application logs for detailed error messages
4. Ensure you haven't exceeded the daily sending limit (300 emails on the free plan)
5. If using Gmail as the sender address, ensure it's properly verified in your Brevo account

## Alternative Free SMTP Providers

If Brevo doesn't meet your needs, here are some alternatives:

1. **Mailjet**: Offers 200 emails per day for free
2. **SendGrid**: Offers 100 emails per day for free
3. **Mailgun**: Offers 5,000 emails per month for 3 months
4. **Amazon SES**: Very low cost ($0.10 per 1,000 emails) but requires AWS account setup
