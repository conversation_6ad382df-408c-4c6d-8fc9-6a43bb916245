# Stage 1: Base runtime image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
USER root
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Stage 2: Build image with SDK
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
ARG ADD_CERTIFICATES=false
WORKDIR /src

# Copy project files
COPY ["FlairLoop.Api/FlairLoop.Api.csproj", "FlairLoop.Api/"]
COPY ["FlairLoop.Domain/FlairLoop.Domain.csproj", "FlairLoop.Domain/"]
COPY ["FlairLoop.Data/FlairLoop.Data.csproj", "FlairLoop.Data/"]

# Restore dependencies
RUN dotnet restore "./FlairLoop.Api/FlairLoop.Api.csproj"

# Copy source files
COPY . .

# Build the application
WORKDIR "/src/FlairLoop.Api"
RUN dotnet build "./FlairLoop.Api.csproj" -c $BUILD_CONFIGURATION -o /app/build

RUN mkdir -p /app/https/
RUN if [ "$ADD_CERTIFICATES" = "true" ]; then \
        cp -r certificates/* /app/https/; \
    fi

# Install dotnet-ef tool in the build stage
RUN dotnet tool install --global dotnet-ef
ENV PATH="$PATH:/root/.dotnet/tools"

# Publish the application
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./FlairLoop.Api.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# Copy certificates only if ADD_CERTIFICATES is true
# This will be done in a separate stage
#FROM build AS certs
#RUN mkdir -p /app/https/
#ARG ADD_CERTIFICATES=false
#RUN if [ "$ADD_CERTIFICATES" = "true" ]; then \
        #cp -r FlairLoop.Api/certificates/* /app/https/; \
    #fi

# Stage 3: Final runtime image
FROM build AS final
WORKDIR /app

# Install curl and bash for wait-for-it script
RUN apt-get update && apt-get install -y curl bash netcat-traditional
# Create directory for SSL certificates
#RUN mkdir -p /app/https/
#COPY ["FlairLoop.Api/certificates/", "/app/https/"]
# Set read permissions for all users
#RUN chmod 644 /app/https/certificate.pfx  
# Step that can be skipped
# Create directory for SSL certificates if needed

# Copy certificates only if ADD_CERTIFICATES is true
#COPY ["FlairLoop.Api/certificates/", "/app/https/"] || true
COPY --from=build /app/https/ /app/https/
# Set permissions for the certificate if it exists
RUN if [ -f /app/https/certificate.pfx ]; then \
        chmod 644 /app/https/certificate.pfx; \
    fi

# Copy wait-for-it script from the build context
COPY ["FlairLoop.Api/wait-for-it.sh", "/app/wait-for-it.sh"]
RUN chmod +x /app/wait-for-it.sh

# Copy the published files from the publish stage
COPY --from=publish /app/publish ./
COPY --from=build /root/.dotnet/tools /root/.dotnet/tools
#ENV PATH="$PATH:/root/.dotnet/tools"

# Create an entry script
COPY ["FlairLoop.Api/entrypoint.sh", "/app/entrypoint.sh"]
RUN chmod +x /app/entrypoint.sh

ENTRYPOINT ["/app/entrypoint.sh"]