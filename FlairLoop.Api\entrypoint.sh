#!/bin/bash
set -e

# Function to wait for PostgreSQL
#wait_for_postgres() {
 #   echo "Waiting for PostgreSQL..."
  #  for i in {1..30}; do
   #     if /app/wait-for-it.sh postgres:5432 -t 1; then
    #        echo "PostgreSQL is up"
     #       return 0
      #  fi
       # echo "Waiting for PostgreSQL... attempt $i/30"
        #sleep 1
    #done
    #echo "Failed to connect to PostgreSQL after 30 attempts"
    #return 1
#}

# Wait for PostgreSQL to be ready
#wait_for_postgres

# Run migrations with retry logic
echo "Running migrations..."
cd /src/FlairLoop.Api

MAX_RETRIES=5
RETRY_COUNT=0

while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    if dotnet ef database update; then
        echo "Migrations completed successfully"
        break
    else
        RETRY_COUNT=$((RETRY_COUNT + 1))
        if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
            echo "Failed to run migrations after $MAX_RETRIES attempts"
            exit 1
        fi
        echo "Migration failed, retrying in 5 seconds... (Attempt $RETRY_COUNT of $MAX_RETRIES)"
        sleep 5
    fi
done

# Return to app directory and start the application
cd /app
#rm -rf /src
#rm -rf /app/build
echo "Starting application..."

exec dotnet FlairLoop.Api.dll