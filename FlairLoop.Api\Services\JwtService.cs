﻿using FlairLoop.Api.Identity;
using FlairLoop.Api.Options;
using FlairLoop.Domain.Auth;
using FlairLoop.Domain.Providers;
using FlairLoop.Domain.Services;
using FlairLoop.Domain.Shared;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace FlairLoop.Api.Services;

public class JwtService(
    IOptions<JwtOptions> tokenOptions,
    TokenValidationParameters tokenValidationParameters,
    IRefreshTokenProvider refreshTokenProvider,
    IClaimsTransformation claimsTransformation,
    UserManager<ApplicationUser> userManager) : IJwtService
{
    private readonly JwtOptions _tokenOptions = tokenOptions.Value;
    private readonly TokenValidationParameters _tokenValidationParameters = tokenValidationParameters;
    private readonly IRefreshTokenProvider _refreshTokenProvider = refreshTokenProvider;
    private readonly IClaimsTransformation _claimTransformation = claimsTransformation;
    private readonly UserManager<ApplicationUser> _userManager = userManager;

    public async Task<Result<AccessTokenInfo>> GenerateToken(ClaimsPrincipal principal)
    {
        var claimPrinciple = await _claimTransformation.TransformAsync(principal);

        var claims = new List<Claim>();

        var userId = principal.GetUserId();

        claims.Add(new(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()));
        claims.Add(new(JwtRegisteredClaimNames.Sub, userId.ToString()));
        claims.Add(new(JwtRegisteredClaimNames.Email, principal.GetUserEmail()));
        claims.Add(new(ApplicationClaimTypes.Id, userId.ToString()));
        claims.AddRange(claimPrinciple.Claims);

        var handler = new JwtSecurityTokenHandler();

        var token = handler.CreateToken(new SecurityTokenDescriptor()
        {
            Subject = new ClaimsIdentity(claims),
            SigningCredentials = new SigningCredentials(
                new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_tokenOptions.Secret)),
                SecurityAlgorithms.HmacSha256Signature),
            Issuer = _tokenOptions.ValidIssuer,
            Audience = _tokenOptions.ValidAudience,
            Expires = DateTime.UtcNow.AddSeconds(_tokenOptions.AccessTokenLifeTime),
        });
        var user = await _userManager.GetUserAsync(principal);
        var accessToken = handler.WriteToken(token);
        await _userManager.SetAuthenticationTokenAsync(user!, "FlairLoop.Auth", "AccessToken", accessToken);
        var refreshToken = new RefreshToken(
            token.Id,
            DateTime.UtcNow,
            DateTime.UtcNow.AddDays(_tokenOptions.RefreshTokenLifeTime),
            userId);

        await refreshTokenProvider.CreateRefreshTokenAsync(refreshToken);

        return Result.Success(new AccessTokenInfo
        {
            AccessToken = accessToken,
            RefreshToken = refreshToken.Token.ToString(),
            ExpiresIn = _tokenOptions.AccessTokenLifeTime * 60,
        });
    }

    public async Task<Result> RevokeToken(ClaimsPrincipal principal)
    {
        var jti = principal.Claims
                .Single(x => x.Type == JwtRegisteredClaimNames.Jti).Value;

        var storedRefreshToken = await refreshTokenProvider.GetRefreshTokenByJtiAsync(jti);

        if (storedRefreshToken is not null)
        {
            storedRefreshToken.MarkAsUsed();
            storedRefreshToken.Invalidate();
            refreshTokenProvider.UpdateRefreshToken(storedRefreshToken);

            return Result.Success();
        }

        return Result.Failure(
            Error.Validation("RefreshToken.NotFound", "Refresh Token not found"));
    }

    public async Task<Result<AccessTokenInfo>> RefreshToken(string accessToken, string refreshToken)
    {
        var result = await GetPrincipalFromToken(accessToken, refreshToken);

        if (result.IsFailure)
        {
            return Result.Failure<AccessTokenInfo>(result?.Error!);
        }

        return await GenerateToken(result.Value!);
    }

    private async Task<Result<ClaimsPrincipal>> GetPrincipalFromToken(string accessToken, string refreshToken)
    {
        var tokenHandler = new JwtSecurityTokenHandler();

        try
        {
            var validationParameters = new TokenValidationParameters()
            {
                ValidateIssuer = _tokenValidationParameters.ValidateIssuer,
                ValidateAudience = _tokenValidationParameters.ValidateAudience,
                ValidAudience = _tokenValidationParameters.ValidAudience,
                ValidateLifetime = false,
                ValidateIssuerSigningKey = _tokenValidationParameters.ValidateIssuerSigningKey,
                ValidIssuer = _tokenValidationParameters.ValidIssuer,
                IssuerSigningKey = _tokenValidationParameters.IssuerSigningKey,
                ClockSkew = _tokenValidationParameters.ClockSkew
            };

            var principal = tokenHandler.ValidateToken(
                accessToken,
                validationParameters,
                out var validatedToken);

            var jti = principal.Claims
                .Single(x => x.Type == JwtRegisteredClaimNames.Jti).Value;

            var storedRefreshToken = await refreshTokenProvider.GetRefreshTokenAsync(refreshToken);

            if (storedRefreshToken?.IsActive() is false || storedRefreshToken?.JwtId != jti)
            {
                return Result.Failure<ClaimsPrincipal>(
                    Error.Validation("RefreshToken.Invalid", "Invalid Refresh Token"));
            }

            storedRefreshToken.MarkAsUsed();

            refreshTokenProvider.UpdateRefreshToken(storedRefreshToken);

            return Result.Success(principal);
        }
        catch (Exception ex)
        {
            return Result.Failure<ClaimsPrincipal>(
                Error.Failure("GetPrincipalFromToken.Failure", ex.Message));
        }
    }
}