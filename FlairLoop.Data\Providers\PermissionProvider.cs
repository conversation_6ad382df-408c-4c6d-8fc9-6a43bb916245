﻿using FlairLoop.Domain.Auth;
using FlairLoop.Domain.Providers;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FlairLoop.Data.Providers;
public class PermissionProvider(ApplicationDbContext dbContext) : IPermissionProvider
{
    public async Task<IEnumerable<Permission>> GetPermissionsAsync()
    {
        return await dbContext.Permissions.ToListAsync();
    }
}
