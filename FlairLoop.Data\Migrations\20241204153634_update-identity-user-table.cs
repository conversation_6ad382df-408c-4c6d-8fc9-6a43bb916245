﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlairLoop.Data.Migrations
{
    /// <inheritdoc />
    public partial class updateidentityusertable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CompanyName",
                schema: "Authentication",
                table: "ApplicationUsers",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Country",
                schema: "Authentication",
                table: "ApplicationUsers",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "FirstName",
                schema: "Authentication",
                table: "ApplicationUsers",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "LastName",
                schema: "Authentication",
                table: "ApplicationUsers",
                type: "text",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CompanyName",
                schema: "Authentication",
                table: "ApplicationUsers");

            migrationBuilder.DropColumn(
                name: "Country",
                schema: "Authentication",
                table: "ApplicationUsers");

            migrationBuilder.DropColumn(
                name: "FirstName",
                schema: "Authentication",
                table: "ApplicationUsers");

            migrationBuilder.DropColumn(
                name: "LastName",
                schema: "Authentication",
                table: "ApplicationUsers");
        }
    }
}
