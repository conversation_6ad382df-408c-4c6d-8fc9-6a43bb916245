﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FlairLoop.Domain.Auth;
public static class ApplicationRoles
{
    public const string Administrator = "Administrator";
    public const string Visitor = "Visitor";

    public static IEnumerable<string> All()
    {
        yield return Administrator;
        yield return Visitor;
    }
}
