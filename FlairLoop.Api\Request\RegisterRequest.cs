﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace FlairLoop.Api.Request;

public sealed class RegisterRequest
{
    [Required]
    [MaxLength(50)]
    public string FirstName { get; set; } = default!;

    [Required]
    [MaxLength(50)]
    public string LastName { get; set; } = default!;

    [Required]
    [MaxLength(100)]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    [Required]
    [PasswordPropertyText]
    [MaxLength(20)]
    public string Password { get; set; } = string.Empty;

    [Required]
    [PasswordPropertyText]
    [MaxLength(20)]
    public string ConfirmPassword { get; set; } = string.Empty;

    [MaxLength(50)]
    public string CompanyName { get; set; } = default!;

    [MaxLength(13)]
    public string PhoneNumber { get; set; } = default!;

    [MaxLength(50)]
    public string Country { get; set; } = default!; 

}
