﻿using FlairLoop.Domain.Auth;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace FlairLoop.Data.Configuration;

public class ApplicationUserConfiguration : IEntityTypeConfiguration<ApplicationUser>
{
    private const string _tableName = "ApplicationUsers";

    public void Configure(EntityTypeBuilder<ApplicationUser> builder)
    {
        builder.ToTable(_tableName);
        builder.Property(u => u.CompanyName).HasMaxLength(50).IsRequired(false);
        builder.Property(u => u.Country).HasMaxLength(50).IsRequired(false);
        builder.Property(u => u.PhoneNumber).HasMaxLength(13).IsRequired(false);

        builder.HasMany(u => u.Claims)
            .WithOne()
            .HasForeignKey(c => c.UserId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(u => u.<PERSON><PERSON>)
            .WithOne()
            .HasForeignKey(l => l.UserId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(u => u.Tokens)
            .WithOne()
            .HasForeignKey(t => t.UserId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(u => u.UserRoles)
            .WithOne()
            .HasForeignKey(ur => ur.UserId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);
    }
}