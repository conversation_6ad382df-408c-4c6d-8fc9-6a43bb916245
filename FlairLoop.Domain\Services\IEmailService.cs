﻿﻿using FlairLoop.Domain.Shared;

namespace FlairLoop.Domain.Services;

public interface IEmailService
{
    /// <summary>
    /// Sends an email to the specified recipient
    /// </summary>
    /// <param name="to">Recipient email address</param>
    /// <param name="subject">Email subject</param>
    /// <param name="body">Email body content (can be HTML)</param>
    /// <param name="isHtml">Whether the body contains HTML content</param>
    /// <returns>Result indicating success or failure</returns>
    Task<Result> SendEmailAsync(string to, string subject, string body, bool isHtml = true);
}
