﻿using FlairLoop.Domain.Auth;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FlairLoop.Domain.Providers;

public interface IRefreshTokenProvider
{
    Task<RefreshToken?> GetRefreshTokenAsync(string token);
    Task<RefreshToken?> GetRefreshTokenByJtiAsync(string jti);
    Task CreateRefreshTokenAsync(RefreshToken refreshToken);
    void UpdateRefreshToken(RefreshToken refreshToken);
}
