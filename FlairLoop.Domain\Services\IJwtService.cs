﻿using FlairLoop.Domain.Auth;
using FlairLoop.Domain.Shared;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace FlairLoop.Domain.Services;

public interface IJwtService
{
    Task<Result<AccessTokenInfo>> GenerateToken(ClaimsPrincipal principal);
    Task<Result> RevokeToken(ClaimsPrincipal principal);
    Task<Result<AccessTokenInfo>> RefreshToken(string accessToken, string refreshToken);
}
