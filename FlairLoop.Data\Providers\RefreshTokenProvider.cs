﻿using FlairLoop.Domain.Auth;
using FlairLoop.Domain.Providers;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FlairLoop.Data.Providers;

public class RefreshTokenProvider(ApplicationDbContext dbContext) : IRefreshTokenProvider
{
    private readonly ApplicationDbContext _dbContext = dbContext;

    public async Task CreateRefreshTokenAsync(RefreshToken refreshToken)
    {
        await _dbContext.RefreshTokens.AddAsync(refreshToken);
        await _dbContext.SaveChangesAsync();
    }

    public async Task<RefreshToken?> GetRefreshTokenAsync(string token)
    {
        return await _dbContext.RefreshTokens
            .SingleOrDefaultAsync(t => t.Token == Guid.Parse(token));
    }

    public async Task<RefreshToken?> GetRefreshTokenByJtiAsync(string jti)
    {
        return await _dbContext.RefreshTokens
           .SingleOrDefaultAsync(t => t.JwtId == jti);
    }

    public void UpdateRefreshToken(RefreshToken refreshToken)
    {
        _dbContext.RefreshTokens.Update(refreshToken);
        _dbContext.SaveChanges();
    }
}
