﻿using FlairLoop.Data;
using FlairLoop.Data.Providers;
using FlairLoop.Domain.Auth;
using FlairLoop.Domain.Providers;
using FlairLoop.Domain.Services;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace FlairLoop.Api.Services;

public class PermissionService(
    IPermissionProvider permissionProvider,
    RoleManager<ApplicationRole> roleManager,
    UserManager<ApplicationUser> userManager) : IPermissionService
{
    private readonly IPermissionProvider _permissionProvider = permissionProvider;
    private readonly RoleManager<ApplicationRole> _roleManager = roleManager;
    private readonly UserManager<ApplicationUser> _userManager = userManager;
    public async Task<IEnumerable<Permission>> GetPermissionsAsync()
    {
        return await _permissionProvider.GetPermissionsAsync();
    }

    public async Task<IEnumerable<string>> GetPermissionsForUserAsync(Guid userId)
    {
        var user = await _userManager.FindByIdAsync(userId.ToString());

        if (user == null)
        {
            return [];
        }

        var roleNames = await _userManager.GetRolesAsync(user);

        return await _roleManager.Roles
            .Include(r => r.Permissions)
            .Where(r => roleNames.Contains(r.Name!))
            .SelectMany(r => r.Permissions)
            .Select(p => p.Name)
            .Distinct()
            .ToArrayAsync();
    }
}
