﻿using FlairLoop.Domain.Auth;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace FlairLoop.Data.Configuration;

public class ApplicationUserTokenConfiguration : IEntityTypeConfiguration<ApplicationUserToken>
{
    private const string _tableName = "ApplicationUserTokens";

    public void Configure(EntityTypeBuilder<ApplicationUserToken> builder)
    {
        builder.ToTable(_tableName);
    }
}