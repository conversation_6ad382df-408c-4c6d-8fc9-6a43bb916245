﻿using FlairLoop.Domain.Auth;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace FlairLoop.Data.Configuration;

public class ApplicationUserRoleConfiguration : IEntityTypeConfiguration<ApplicationUserRole>
{
    private const string _tableName = "ApplicationUserRoles";

    public void Configure(EntityTypeBuilder<ApplicationUserRole> builder)
    {
        builder.ToTable(_tableName);
    }
}