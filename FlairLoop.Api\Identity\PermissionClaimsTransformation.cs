﻿using FlairLoop.Domain.Services;
using Microsoft.AspNetCore.Authentication;
using System.Security.Claims;

namespace FlairLoop.Api.Identity;

public class PermissionClaimsTransformation(IServiceScopeFactory serviceScopeFactory)
    : IClaimsTransformation
{
    public async Task<ClaimsPrincipal> TransformAsync(ClaimsPrincipal principal)
    {

        if (principal.Identity?.IsAuthenticated == true
            && !principal.HasClaim(c => c.Type == ApplicationClaimTypes.Permission))
        {
            using var scope = serviceScopeFactory.CreateScope();

            var permissionService = scope.ServiceProvider.GetRequiredService<IPermissionService>();
            var permissions = await permissionService.GetPermissionsForUserAsync(principal.GetUserId());

            var claims = permissions.Select(permission => new Claim(ApplicationClaimTypes.Permission, permission));
            //var claimsIdentity = new ClaimsIdentity();
            //claimsIdentity.AddClaims(claims);
            //principal.AddIdentity(claimsIdentity);
            var identity = principal.Identity as ClaimsIdentity;
            foreach (var claim in claims)
            {
                if (!identity!.HasClaim(c => c.Type == claim.Type && c.Value == claim.Value))
                {
                    identity.AddClaim(claim);
                }
            }
        }

        return principal;
    }
}