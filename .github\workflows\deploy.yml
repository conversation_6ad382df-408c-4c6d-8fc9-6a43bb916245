name: <PERSON><PERSON><PERSON><PERSON> Auth Api CI/CD with Docker Hub

on:
  push:
    branches:
      - master

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      # 1. Checkout code
      - name: Checkout Code
        uses: actions/checkout@v3

      # 2. Upload docker-compose.yml as an artifact
      - name: Upload docker-compose.yml
        uses: actions/upload-artifact@v4
        with:
          name: docker-compose
          path: ./docker-compose.yml

      # 3. Set up .NET
      - name: Set up .NET 8
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: 8.0

      # 4. Log in to Docker Hub
      - name: Log in to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ vars.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_TOKEN }}

      # 5. Build and tag Docker image
      - name: Build and Tag Docker Image
        run: |
          docker build --build-arg ADD_CERTIFICATES=false -t yogendrasgautam/flairloop-api:latest -f FlairLoop.Api/Dockerfile .

      # 6. Push Docker image to Docker Hub
      - name: Push Docker Image
        run: |
          docker push yogendrasgautam/flairloop-api:latest

  deploy:
    runs-on: ubuntu-latest
    needs: build
    steps:
      # 1. Download docker-compose.yml artifact
      - name: Download docker-compose.yml
        uses: actions/download-artifact@v4
        with:
          name: docker-compose

     # Step 2: Transfer docker-compose.yml to the server
      - name: Transfer docker-compose.yml
        uses: appleboy/scp-action@v0.1.4
        with:
         host: ${{ secrets.SERVER_HOST }}
         username: ${{ secrets.SERVER_USER }}
         key: ${{ secrets.SSH_PRIVATE_KEY }}
         port: 22
         source: docker-compose.yml
         target: /root/deployment/flairloop-api/

      # 2. Deploy to Linux Server
      - name: Deploy to Linux Server
        uses: appleboy/ssh-action@v0.1.7
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            mkdir -p '/root/deployment/flairloop-api/app/cert/'
            cp '/etc/letsencrypt/live/flairloop.com/certificate.pfx' '/root/deployment/flairloop-api/app/cert/'
          
            cd /root/deployment/flairloop-api/
            # List files in the home directory to verify the copy
            ls -la 
          
            # Check if docker-compose.yml exists
            if [ ! -f docker-compose.yml ]; then
              echo "docker-compose.yml not found on the server!"
              exit 1
            fi

            # Export environment variables
            echo "ENVIRONMENT=Production" > /root/deployment/flairloop-api/.env
            echo "CERT_HOST_PATH=/root/deployment/flairloop-api/cert" >> /root/deployment/flairloop-api/.env
            echo "IMAGE_NAME=yogendrasgautam/flairloop-api:latest" >> /root/deployment/flairloop-api/.env
            echo "ASPNETCORE_HTTPS_PORT=443" >> /root/deployment/flairloop-api/.env
            echo "ASPNETCORE_URLS=http://+:80;https://+:443" >> /root/deployment/flairloop-api/.env
            echo "POSTGRES_PASSWORD=${{ secrets.DB_PASSWORD }}" >> /root/deployment/flairloop-api/.env
            echo "CONN_DATABASE=Host=postgres;Port=5432;Database=flairloop_db;Username=flairloop_user;Password=${{ secrets.DB_PASSWORD }}" >> /root/deployment/flairloop-api/.env
            echo "Kestrel__Certificates__Default__Path=/app/https/certificate.pfx" >> /root/deployment/flairloop-api/.env 
            echo "Kestrel__Certificates__Default__Password=${{ secrets.CERT_PASSWORD }}" >> /root/deployment/flairloop-api/.env 
            echo "JWT_SECRET=${{ secrets.JWT_SECRET }}" >> /root/deployment/flairloop-api/.env
            echo "ADMIN_EMAIL=${{ secrets.ADMIN_EMAIL }}" >> /root/deployment/flairloop-api/.env
            echo "ADMIN_PASSWORD=${{ secrets.ADMIN_PASSWORD }}" >> /root/deployment/flairloop-api/.env

            # Stop and remove existing container
            docker-compose down
            
            # Delete existing images
            docker rmi yogendrasgautam/flairloop-api
            
            # Pull the latest image
            docker pull yogendrasgautam/flairloop-api:latest

            # Start with the updated image
            docker-compose --env-file .env up -d
