using FlairLoop.Api.Options;
using FlairLoop.Domain.Services;
using FlairLoop.Domain.Shared;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net;
using System.Net.Mail;

namespace FlairLoop.Api.Services;

public class EmailService : IEmailService
{
    private readonly EmailOptions _emailOptions;
    private readonly ILogger<EmailService> _logger;

    public EmailService(IOptions<EmailOptions> emailOptions, ILogger<EmailService> logger)
    {
        _emailOptions = emailOptions.Value;
        _logger = logger;
    }

    public async Task<Result> SendEmailAsync(string to, string subject, string body, bool isHtml = true)
    {
        try
        {
            _logger.LogInformation("Attempting to send email to {Recipient} using SMTP server {SmtpServer}:{SmtpPort}",
                to, _emailOptions.SmtpServer, _emailOptions.SmtpPort);

            // Validate email configuration
            if (string.IsNullOrWhiteSpace(_emailOptions.SmtpUsername) ||
                string.IsNullOrWhiteSpace(_emailOptions.SmtpPassword))
            {
                _logger.LogError("SMTP credentials are not configured properly");
                return Result.Failure(Error.Failure("Email.Configuration", "SMTP credentials are not configured"));
            }

            using var client = new SmtpClient(_emailOptions.SmtpServer, _emailOptions.SmtpPort)
            {
                EnableSsl = _emailOptions.EnableSsl,
                UseDefaultCredentials = false, // Explicitly disable default credentials
                Credentials = new NetworkCredential(_emailOptions.SmtpUsername, _emailOptions.SmtpPassword),
                DeliveryMethod = SmtpDeliveryMethod.Network,
                Timeout = 30000 // 30 seconds timeout
            };

            using var message = new MailMessage
            {
                From = new MailAddress(_emailOptions.FromEmail, _emailOptions.FromName),
                Subject = subject,
                Body = body,
                IsBodyHtml = isHtml
            };

            message.To.Add(to);
            message.CC.Add(_emailOptions.ToInternalEmail);
            message.CC.Add(_emailOptions.CCEmail);

            _logger.LogInformation("Connecting to SMTP server with username: {Username}", _emailOptions.SmtpUsername);
            await client.SendMailAsync(message);
            _logger.LogInformation("Email sent successfully to {Recipient}", to);
            return Result.Success();
        }
        catch (SmtpException smtpEx)
        {
            var statusCode = smtpEx.StatusCode;
            _logger.LogError(smtpEx, "SMTP error occurred while sending email to {Recipient}. Status code: {StatusCode}",
                to, statusCode);

            string errorMessage = statusCode switch
            {
                SmtpStatusCode.ServiceNotAvailable => "SMTP service not available. Please try again later.",
                SmtpStatusCode.MailboxBusy => "Recipient mailbox is busy. Please try again later.",
                SmtpStatusCode.MailboxUnavailable => "Recipient mailbox is unavailable.",
                SmtpStatusCode.TransactionFailed => "Email transaction failed. Please check SMTP credentials and ensure the account is properly authenticated with Brevo.",
                SmtpStatusCode.GeneralFailure => "Failed to send email due to a general SMTP failure.",
                SmtpStatusCode.CommandNotImplemented => "SMTP authentication failed. Please verify your Brevo SMTP credentials and ensure the account is active.",
                _ => $"SMTP error: {smtpEx.Message}. If this is an authentication error, please verify your Brevo SMTP key is valid and the sender email is verified."
            };

            return Result.Failure(Error.Failure("Email.SmtpError", errorMessage));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email to {Recipient}", to);
            return Result.Failure(Error.Failure("Email.GeneralError", $"Failed to send email: {ex.Message}"));
        }
    }
}
