IMAGE_NAME=flairloop/api:latest
ENVIRONMENT=Docker
CERT_HOST_PATH=./certificates
Kestrel__Certificates__Default__Path=/app/https/certificate.pfx
Kestrel__Certificates__Default__Password=emptyNebul@15
POSTGRES_PASSWORD=emptyNebul@15
CONN_DATABASE=Host=postgres;Port=5432;Database=flairloop_db;Username=flairloop_user;Password=emptyNebul@15
JWT_SECRET=XVCpHgOkn8ewoLJkz5cb4CND4lPy9DwuDSUZQVcZNw4=
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=emptyNebul@31
EMAIL_SMTP_SERVER=smtp-relay.brevo.com
EMAIL_SMTP_PORT=587
EMAIL_SMTP_USERNAME=<EMAIL>
EMAIL_SMTP_PASSWORD=your-brevo-smtp-key-here
EMAIL_FROM_EMAIL=<EMAIL>
EMAIL_FROM_NAME=FlairLoop
EMAIL_ENABLE_SSL=true