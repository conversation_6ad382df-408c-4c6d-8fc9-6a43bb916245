# PowerShell script to test email functionality
# Run this script to test if the email service is working correctly

$apiUrl = "http://localhost:5220/api/email/notify-admin"

$emailData = @{
    to = "<EMAIL>"  # Replace with your test email
    subject = "Test Email from FlairLoop - $(Get-Date)"
    body = "<h1>Hello from Flair<PERSON>oop!</h1><p>This is a test email sent from the FlairLoop API at $(Get-Date).</p><p>If you receive this email, the SMTP configuration is working correctly.</p>"
    isHtml = $true
} | ConvertTo-Json

Write-Host "Testing email functionality..." -ForegroundColor Yellow
Write-Host "API URL: $apiUrl" -ForegroundColor Cyan
Write-Host "Email Data: $emailData" -ForegroundColor Cyan

try {
    $response = Invoke-RestMethod -Uri $apiUrl -Method POST -Body $emailData -ContentType "application/json"
    Write-Host "✅ Email sent successfully!" -ForegroundColor Green
    Write-Host "Response: $response" -ForegroundColor Green
}
catch {
    Write-Host "❌ Failed to send email:" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
    }
}

Write-Host "`nNote: Make sure to:" -ForegroundColor Yellow
Write-Host "1. Update the SMTP password in appsettings.Development.json with a valid Brevo SMTP key" -ForegroundColor White
Write-Host "2. Replace '<EMAIL>' with your actual email address" -ForegroundColor White
Write-Host "3. Ensure the FlairLoop API is running on localhost:5220" -ForegroundColor White
