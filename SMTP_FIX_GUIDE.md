# SMTP Authentication Fix Guide

## Problem
You're getting the error: `SMTP error: Command not implemented. The server response was: 5.7.0 Please authenticate first`

## Root Cause
The SMTP authentication is failing because:
1. The SMTP credentials might be invalid or expired
2. The email address configuration was inconsistent
3. The SMTP client wasn't properly configured for authentication

## What I've Fixed

### 1. Updated Email Configuration
- ✅ Changed email addresses from `<EMAIL>` to `<EMAIL>` (as per your memory)
- ✅ Updated all configuration files consistently
- ✅ Set placeholder for SMTP password that needs to be updated

### 2. Enhanced EmailService
- ✅ Added explicit `UseDefaultCredentials = false`
- ✅ Added `DeliveryMethod = SmtpDeliveryMethod.Network`
- ✅ Increased timeout to 30 seconds
- ✅ Added validation for SMTP credentials
- ✅ Enhanced error handling for authentication issues
- ✅ Added more detailed logging

### 3. Files Modified
- `FlairLoop.Api/appsettings.Development.json`
- `FlairLoop.Api/appsettings.Docker.json`
- `docker-local.env`
- `FlairLoop.Api/Services/EmailService.cs`

## Next Steps - YOU NEED TO DO THIS

### Step 1: Get Valid Brevo SMTP Credentials
1. Go to [Brevo](https://www.brevo.com/) and log in to your account
2. Navigate to **SMTP & API** → **SMTP**
3. Generate a new SMTP key if needed
4. Copy the SMTP key

### Step 2: Update Configuration
Replace `"your-brevo-smtp-key-here"` in `FlairLoop.Api/appsettings.Development.json` with your actual Brevo SMTP key:

```json
"SmtpPassword": "your-actual-brevo-smtp-key-here"
```

### Step 3: Verify Email Address in Brevo
1. In your Brevo account, go to **Senders & IP**
2. Make sure `<EMAIL>` is added and verified as a sender
3. If not, add it and complete the verification process

### Step 4: Test the Configuration
1. Start your API: `dotnet run --project FlairLoop.Api`
2. Run the test script: `.\test-email.ps1`
3. Or use the HTTP file: `FlairLoop.Api/FlairLoop.Api.http`

## Alternative SMTP Providers (if Brevo doesn't work)

### Option 1: Mailjet
- SMTP Server: `in-v3.mailjet.com`
- Port: `587`
- Free tier: 200 emails/day

### Option 2: SendGrid
- SMTP Server: `smtp.sendgrid.net`
- Port: `587`
- Free tier: 100 emails/day

### Option 3: Mailgun
- SMTP Server: `smtp.mailgun.org`
- Port: `587`
- Free tier: 5,000 emails/month for 3 months

## Troubleshooting

### If you still get authentication errors:
1. **Double-check the SMTP key** - Make sure it's copied correctly without extra spaces
2. **Verify sender email** - Ensure `<EMAIL>` is verified in your Brevo account
3. **Check account status** - Make sure your Brevo account is active and not suspended
4. **Try regenerating the SMTP key** - Sometimes keys expire or get corrupted

### If you get "Service not available":
1. Check if Brevo's SMTP service is down
2. Try using a different SMTP port (465 with SSL, or 25)
3. Check your firewall/network settings

### For Docker deployment:
Update the environment variable in `docker-local.env`:
```
EMAIL_SMTP_PASSWORD=your-actual-brevo-smtp-key-here
```

## Testing Commands

### Test via PowerShell:
```powershell
.\test-email.ps1
```

### Test via curl:
```bash
curl -X POST http://localhost:5220/api/email/send \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "subject": "Test Email",
    "body": "<h1>Test</h1>",
    "isHtml": true
  }'
```

### Test via HTTP file:
Open `FlairLoop.Api/FlairLoop.Api.http` in Visual Studio and run the email test request.

## Success Indicators
- ✅ No SMTP authentication errors in logs
- ✅ Email appears in recipient's inbox
- ✅ API returns 200 OK status
- ✅ Logs show "Email sent successfully"
