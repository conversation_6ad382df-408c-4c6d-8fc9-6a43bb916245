﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FlairLoop.Domain.Auth;

public class Permission
{
    public int Id { get; }

    public string Name { get; }

    public byte[]? RowVersion { get; }

    private Permission(string name)
    {
        Name = name;
    }

    private Permission()
    { }

    public static Permission Create(string name)
    {
        return new Permission(name);
    }
}
