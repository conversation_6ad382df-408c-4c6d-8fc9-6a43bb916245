﻿using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FlairLoop.Domain.Auth;

public class ApplicationUser : IdentityUser<Guid>
{
    public string FirstName { get; set; } = default!;
    public string LastName { get; set; } = default!;
    public string CompanyName { get; set; } = default!;
    public string Country { get; set; } = default!;
    public ICollection<ApplicationUserClaim> Claims { get; } = [];
    public ICollection<ApplicationUserLogin> Logins { get; } = [];
    public ICollection<ApplicationUserToken> Tokens { get; } = [];
    public ICollection<ApplicationUserRole> UserRoles { get; } = [];

    private ApplicationUser() { }

    public static ApplicationUser Create()
    {
        return new ApplicationUser();
    }
}
